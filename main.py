import os, requests
from crewai import Agent, Task, Crew
from langchain_openai import <PERSON>t<PERSON>penA<PERSON>
from pydantic import BaseModel, Field
from typing import Type
from importlib import import_module

# === Setup ===
llm = ChatOpenAI(model="gpt-4", temperature=0.2)
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

# === Dynamically import BaseTool ===
BaseTool = None
for path in ["crewai.tools.BaseTool", "crewai.tools.base_tool.BaseTool", "crewai.BaseTool"]:
    try:
        module, attr = path.rsplit(".", 1)
        BaseTool = getattr(import_module(module), attr)
        break
    except (ImportError, AttributeError):
        continue

if not BaseTool:
    from crewai_tools import FileReadTool as BaseTool

# === FAISS Tool Definition ===
class FAISSSearchInput(BaseModel):
    query: str = Field(..., description="The search query for Power BI proposals")

class FAISSSearchTool(BaseTool):
    name: str = "faiss_search_tool"
    description: str = "Search Power BI proposals for relevant past content."
    args_schema: Type[BaseModel] = FAISSSearchInput

    def _run(self, query: str) -> str:
        try:
            response = requests.post(
                url="http://*************:8080/proposal/faiss/search",
                json={"query": query},
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            results = response.json()

            # Normalize response format
            results = results.get('results') or results.get('data') or results.get('documents') or results
            if not isinstance(results, list):
                return f"Unexpected response format: {type(results)}"

            chunks = [
                item.get('text') or item.get('content') or item.get('document') or str(item)
                for item in results[:5]
            ]
            return "\n\n".join(chunks) if chunks else "No relevant results found."

        except Exception as e:
            return f"Error querying FAISS API: {e}"

# === Crew Setup ===
faiss_search_tool = FAISSSearchTool()
retriever = Agent(
    role="Proposal Research Agent",
    goal="Search and extract relevant content from historical Power BI proposals",
    backstory="You are skilled in identifying reusable patterns from previous proposals.",
    tools=[faiss_search_tool],
    verbose=True,
    llm=llm
)

task = Task(
    description="Search for relevant Power BI proposal content related to project duration and effort.",
    agent=retriever,
    expected_output="A summary of relevant excerpts from past proposals about typical project durations."
)

# === Run ===
result = Crew(agents=[retriever], tasks=[task]).kickoff()
print("\n=== Final Output ===\n", result)
